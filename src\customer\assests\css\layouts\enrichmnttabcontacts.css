@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap');


body {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    /* Regular */
}

button.Verifyingemailv2 {
    border: 1px solid #DDDDDD;
    width: 165px;
    text-align: justify;
    padding: 2px 0 2px 13px;
    background-color: #F5F5F5;
    font-family: 'Poppins', sans-serif;
    border-radius: 4px;
}

.enrich-tab-list-button {
    border: none;
    background: transparent;
    padding: 2px 15px;
    border-radius: 999px;
    font-size: 12px;
    color: #333;
    transition: background-color 0.3s, color 0.5s;
    cursor: pointer;
    font-family: 'Poppins', sans-serif;

}

.enrich-tab-list-button.active {
    background-color: #F1F2F4;
    border: 2px solid #115EA3;
    color: #000;
    cursor: pointer;
    font-size: 12px;
    font-family: 'Poppins', sans-serif;


}

.enrich-tab-list-button:focus {
    outline: none !important;
    box-shadow: none !important;
    font-family: 'Poppins', sans-serif;

}

.enrich-tab-wrapper.mt-2.mb-3 {
    background-color: #fff;
    width: 174px;
    border-radius: 23px;
    box-shadow: inset 0px 1px 6px #00000029;

}

.tab-content-area {
    transition: all 0.3s ease;
    /* border: 1px solid #ECECEC;  */
    /* box-shadow: 0 0 6px #ECECEC; */
}



/* Smooth fade animation */
.fade-tab {
    opacity: 0;
    transform: translateY(5px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-tab.fade-in {
    opacity: 1;
    transform: translateY(0);
}

.fade-tab.fade-out {
    opacity: 0;
    transform: translateY(5px);
}


.enrich-custom-table {
    width: 100%;
    border-collapse: collapse;
}

.enrich-table-head,
.enrich-table-body {
    background-color: #ffffff;
    border-bottom: 2px solid #ECECEC;
}

.enrich-table-body .enrich-table-cell {
    font-size: 14px;
    color: #283547;
    padding: 10px 12px;
    border: none;
    border-bottom: 1px solid #e0e0e0;
    font-family: 'Poppins', sans-serif;

}

.th.enrich-table-header {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    border: 0;
    font-size: 12px;
    padding: 8px 13px 8px 13px;
    color: #737373;
    font-family: 'Poppins', sans-serif;

}

button.rtdownload {
    background-color: #115EA3;
    border: 0;
    outline: none;
    font-size: 12px;
    margin: 0 5px 0 0px;
    color: #fff;
    border-radius: 4px;
    padding: 3px 10px 3px 10px;
    cursor: pointer;
}

th.enrich-table-header {
    font-size: 12px;
    font-weight: 400;
    color: #737373;
    padding: 8px 8px;
    font-family: 'Poppins', sans-serif;

}

.deleted-csv-file img {
    background-color: #FAFAFA;
    padding: 3px 6px 3px 6px;
    border: 1px solid #ECECEC;
    border-radius: 4px;
    width: 28px;
    cursor: pointer;
}

p.csv-companies-enrich {
    text-align: center;
    font-weight: 600;
    margin: 1rem 0 8px 0;
}

p.reachstream-database {
    text-align: center;
    font-size: 14px;
}

.enrich-tab-button-2.py-2 {
    font-size: 12px;
    padding: 0 20px 0 20px;
    margin: 0;
    border-bottom: 2px solid #1473E6;
}


p.download-sample {
    font-size: 14px;
    margin: 3px 8px 0 8px;
    color: #666666;
    font-weight: 400;
    cursor: pointer;
    font-family: 'Poppins', sans-serif;

}

.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}


.popup-box {
    padding: 2rem;
    border-radius: 8px;
    width: 75%;
    text-align: center;
    position: relative;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    animation: fadeInScale 0.3s ease-in-out;
    background: #fff;
    /* background: linear-gradient(244deg, rgba(237, 240, 244, 1) 0%, rgba(244, 243, 244, 1) 50%); */
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}

.close-button {
    position: absolute;
    top: 10px;
    right: 14px;
    background: transparent;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
}

.upload-btn {
    background-color: #115EA3;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
}

@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.95);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}



.enrich-table-container {
    /* height: 100vh;  */
    /* overflow-y: auto; */
    background-color: #fff;
  }
  
  .enrich-custom-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
  }
  
  .enrich-table-header,
  .enrich-table-cell {
    vertical-align: middle;
    /* padding: 5px 12px; */
    white-space: nowrap;
    text-align: left;
  }
  
  
  .rtdownload {
    padding: 4px 8px;
    font-size: 12px;
    background-color: #007bff;
    border: none;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
  }
  