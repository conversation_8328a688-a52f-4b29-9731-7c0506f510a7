import React from 'react';
import { useState } from 'react';
import { envConfig } from "../../config";

export const domain = {
	// 'AAA_URL': 'https://api-prd.reachstream.com/',
	// 'url': 'https://api-prd.reachstream.com/',
	'url': envConfig.apiUrl,
	// 'AAA_URL': 'https://aaa-prd.reachstream.com/',
	'EMAIL_URL': 'http://*************:8000/',
	// 'EMAIL_URL': 'http://localhost:8000/',   
	// 'SEARCH': 'http://stg-rs-fe.reachstream.com/search/'
}

export const ApiName = {

	'superSignUp': domain.url + 'AAA/v1/auth/register',
	'signIn': domain.url + 'AAA/v1/auth/login',
	'forgotPassword': domain.url + 'AAA/v1/auth/forget-password',
	'verifyEmail': domain.url + 'AAA/v1/auth/forget-password/verify-email',
	'createNewPassword': domain.url + 'AAA/v1/auth/forget-password/create-new-password',
	'superSaverDollar': domain.url + 'Pricing/v1/package/price-plan',
	'checkPromocode': domain.url + 'Pricing/v1/promocode/name/',
	'confirmAndPay': domain.url + 'Pricing/v1/payment/check-out-session',
	'transactionHistory': domain.url + 'Pricing/v1/orders/transaction/details',
	'userData': domain.url + 'AAA/v1/user/contact',
	'profileUpdate': domain.url + 'AAA/v1/user/profile/update',
	'activeCreadits': domain.url + 'AAA/v1/user/plan/active/credit',
	'updateCreadits': domain.url + 'AAA/v1/user/plan/update',
	'passingRecordIds': domain.url + 'Search/v1/records/fetch',
	'searchFilters': domain.url + 'Search/v1/records/filters',
	'predefinedValuesFilter': domain.url + 'Search/v1/records/get-predefined-values',
	'fetchSingleRecord': domain.url + 'Search/v1/records/single/fetch',
	'createOrder': domain.url + 'Pricing/v1/orders/create',
	'packagePricePlan': domain.url + 'Pricing/v1/package/price-plan',
	'activeCredit': domain.url + 'AAA/v1/user/plan/active/credit',
	'updateCredit': domain.url + 'AAA/v1/user/plan/update',
	'contactUs': domain.url + 'AAA/v1/contact/contact-us',
	'userProfile': domain.url + 'AAA/v1/user/profile',
	'createUserPlan': domain.url + 'AAA/v1/user/plan/create',
	'profilePictureUpload': domain.url + 'AAA/v1/user/profile/picture/upload',
	'getProfilePicture': domain.url + 'AAA/v1/user/profile/picture',
	'sendActivationCode': domain.url + 'AAA/v1/user/send/account-activation',
	'superSaverPlanRegister': domain.url + 'AAA/v1/super-saver-plan-register',
	'freePlanRegister': domain.EMAIL_URL + 'free-plan-register',
	'forgotPasswordEmail': domain.EMAIL_URL + 'forget-password',
	'reachoutUS': domain.EMAIL_URL + 'contact-us',
	'resendVerifyEmail': domain.url + 'AAA/v1/user/resend/account-activation',
	'getAllUsers': domain.url + 'AAA/v1/admin/get-all-users',
	'getAllUserPlansPagination': domain.url + 'AAA/v1/admin/get-all-users-plans',
	'activeOrDeactive': domain.url + 'AAA/v1/admin/update-user-status',
	'getAllPromoWithPagination': domain.url + 'Pricing/v1/promocode/get-all-promo-with-pagination',
	'getAllPromocde': domain.url + 'Pricing/v1/promocode/get-all',
	'createPromocde': domain.url + 'Pricing/v1/promocode/create',
	'updatePromocode': domain.url + 'Pricing/v1/promocode/update',
	'deletePromocode': domain.url + 'Pricing/v1/promocode',
	'createApiKey': domain.url + 'AAA/v1/auth/generate/encrypted/apikey',
	'getApiKey': domain.url + 'AAA/v1/auth/user-apiKey',
	'tempRegister': domain.url + 'AAA/v1/auth/temp-register',
	'activeTempRegister': domain.url + 'AAA/v1/auth/active-temp-register',
	'totalContactCounts': domain.url + 'Search/v1/records/search-pattern/count',
	// 'totalContactCounts': domain.url + 'Search/records/counts/search',

	'companyCompanyName': domain.url + 'Search/v1/records/company-names/search-list',
	'companyCompanyURL': domain.url + 'Search/v1/records/company-website/search-list',
	'downloadedIds': domain.url + 'Search/v1/records/usage/list',
	'downaloadAllApi': domain.url + 'Search/v1/records/search-pattern/data',
	'technologyKeywords': domain.url + 'Search/v1/records/techkeyword/search-list',
	'companyAddressZipcode': domain.url + 'Search/v1/records/zipcode/search-list',
	'companyAddressCity': domain.url + 'Search/v1/records/city/search-list',
	'companyAddressState': domain.url + 'Search/v1/records/state/search-list',
	'companyAddressCountry': domain.url + 'Search/v1/records/country/search-list',
	'contactJobTitle': domain.url + 'Search/v1/records/job-title/search-list',
	'APICreditUsed': domain.url + 'AAA/v1/user/credit-usage/find',
	'createPackegeDetails': domain.url + 'Pricing/v1/package/price-plan',
	'getPackegeDetails': domain.url + 'Pricing/v1/package/price-plan',
	'deletePackegeDetails': domain.url + 'Pricing/v1/package/price-plan/delete',
	'editPackegeDetails': domain.url + 'Pricing/v1/package/price-plan/update',
	'oneTimeCoupon': domain.url + 'Pricing/v1/promocode/validate/one-time-coupon',
	'zipcodeList': domain.url + 'Search/v1/records/zipcode/list',
	'companyName1': domain.url + 'Search/v1/records/company-names/list',
	'createPaymentId': domain.url + 'Pricing/v1/payment/create/price',
	'createPricePlan': domain.url + 'Pricing/v1/package/price-plan',
	'findPricePlanById': domain.url + 'Pricing/v1/package/price-plan/findById ',
	'getReferralCode': domain.url + 'AAA/v1/user/fetch/referral/code',
	'getReferredList': domain.url + 'AAA/v1/user/referred-list',
	'createReferralCode': domain.url + 'AAA/v1/user/create/referral/code',
	'createReferral': domain.url + 'AAA/v1/user/create/referral',
	'updateReferral': domain.url + 'AAA/v1/user/update/referral',
	'updateReferralPoints': domain.url + 'AAA/v1/user/update/referral-points',
	'findReferralPoints': domain.url + 'AAA/v1/user/find/referral-points',
	'notifyCustomPlan': domain.url + 'AAA/v1/user/notify-custom-plan',
	'findReferral': domain.url + 'AAA/v1/user/find/referral',
	'activatePreemiumReferral': domain.url + 'AAA/v1/user/activate/preemium/referral',
	'getAllTemplates': domain.url + 'AAA/v1/user/notification/template',
	'createTemplate': domain.url + 'AAA/v1/user/notification/template/create',
	'updateTemplate': domain.url + 'AAA/v1/user/notification/template/save',
	'createUserNotification': domain.url + 'AAA/v1/user/notification/create',
	'getAllUserNotification': domain.url + 'AAA/v1/user/notification',
	'getNotificationByUserId': domain.url + 'AAA/v1/user/notification/findByUserId',
	'getTemplateByTemplateId': domain.url + 'AAA/v1/user/notification/template/findById',
	'updateNotificationByUser': domain.url + 'AAA/v1/user/notification/save',
	'switchFreemiumToPreemium': domain.url + 'Pricing/v1/payment/manual/update',
	'upgradeEmail': domain.url + 'AAA/v1/user/notify-email',
	'submitSupportNotification': domain.url + 'AAA/v1/user/support/create',
	'submitFeedbackNotification': domain.url + 'AAA/v1/user/support/create',
	'notifyAdmin': domain.url + 'AAA/v1/user/notify-admin',
	'userActivatedByAdmin': domain.url + 'AAA/v1/admin/temp-register/create-active',
	'fetchUsersDomainBlockPagination': domain.url + 'AAA/v1/admin/user-block/paginate',
	'fetchUsersDomainBlockList': domain.url + 'AAA/v1/admin/user-block/list',
	'createUsersDomainBlockList': domain.url + 'AAA/v1/admin/user-block/create',
	'updateUserDomainBlock': domain.url + 'AAA/v1/admin/user-block/save',
	'findUserDomainBlock': domain.url + 'AAA/v1/admin/user-block/findById',
	'deleteUserDomainBlock': domain.url + 'AAA/v1/admin/user-block/delete',
	'fetchDomainRestrictionPagination': domain.url + 'AAA/v1/admin/domain-restriction/paginate',
	'fetchDomainRestrictionList': domain.url + 'AAA/v1/admin/domain-restriction/list',
	'createDomainRestriction': domain.url + 'AAA/v1/admin/domain-restriction/create',
	'updateDomainRestriction': domain.url + 'AAA/v1/admin/domain-restriction/save',
	'findDomainRestriction': domain.url + 'AAA/v1/admin/domain-restriction/findById',
	'deleteDomainRestriction': domain.url + 'AAA/v1/admin/domain-restriction/delete',
	'redeemReferralPoints': domain.url + 'AAA/v1/user/redeem/referral-points',
	'activeDomainBlockList': domain.url + 'AAA/v1/auth/active-domain-block-list',
	'resetPassword': domain.url + 'AAA/v1/admin/reset-user-password',
	'deviceLogout': domain.url + 'device-logout',
	'deviceLogin': domain.url + 'device-login',
	'fetchSingleUser': domain.url + 'AAA/v1/admin/user-details',
	'noOfDeviceLogin': domain.url + 'AAA/v1/admin/set-user-device-allowed-limit',
	'updateAggrement': domain.url + 'AAA/v1/auth/user/update',
	'getAggrement': domain.url + 'AAA/v1/user/contact',
	'deactivateUserAccount': domain.url + 'AAA/v1/user/account/deactivation',
	'cancelSubscriptionAccount': domain.url+ 'AAA/v1/user/account/cancel/subscription',
	'socialmediaregister': domain.url + 'AAA/v1/auth/register',
	'alllogs': domain.url + 'AAA/v1/user/notification/findAllPagination',
	'findUsersByNotificationList': domain.url + 'AAA/v1/user/notification/findUsersByNotificationList',
	'userFeedback': domain.url + 'AAA/v1/user/feedback/notify',
	'pricePlanePagination': domain.url + 'Pricing/v1/package/price-plan/paginate',

	'createList': domain.url + 'AAA/v1/wish-list/create',
	'fetchAllList': domain.url + 'AAA/v1/wish-list/fetch-all',
	'updateList': domain.url + 'AAA/v1/wish-list/update',
	'fetchList': domain.url + 'AAA/v1/wish-list/fetch',
	'fetchListNames': domain.url + 'AAA/v1/wish-list/fetch-names',
	'deleteList': domain.url + 'AAA/v1/wish-list/delete',
	'listItemCreate': domain.url + 'AAA/v1/wish-list-items/create',
	'listItemDelete': domain.url + 'AAA/v1/wish-list-items/delete',
	'listItemDeleteByIds': domain.url + 'AAA/v1/wish-list-items/deleteByIds',
	'getAllListRecords': domain.url + 'Search/v1/records/wish-list/filters',
	'filterHistoryCreate': domain.url + 'user/filter/history/create',
	'promocodeHistory': domain.url + 'Pricing/v1/promocode/history/fetchAll',
	'deviceHistory': domain.url + 'user/device/access/history',

	'searchJobTitle': domain.url + 'Search/v1/records/job-titles/search-pattern',
	'searchCity': domain.url + 'Search/v1/records/city/search-pattern',
	'searchState': domain.url + 'Search/v1/records/states/search-pattern',
	'searchCountry': domain.url + 'Search/v1/records/country/search-pattern',
	'searchZipCode': domain.url + 'Search/v1/records/zip-code/search-pattern',
	'searchIndustry': domain.url + 'Search/v1/records/industry/search-pattern',
	'searchSICCode': domain.url + 'Search/v1/records/sic-code/search-pattern',

	'revealEmail': domain.url + 'Search/v1/emailverifier/history/fetch-by-data-id',
	'emailVerifyHistory': domain.url + 'Search/v1/emailverifier/history/fetch-email-verifier-by-data-ids',
	'revealEmailByWishList': domain.url + 'Search/v1/emailverifier/history/fetch-by-wish-list-id',

	'fetchUserHistory': domain.url + 'user/filter/history/fetch-all',
	'userFilterUsageHistoryStats': domain.url + 'user/user-filter-usage-history-stats',
	'totalHours': domain.url + 'user/total/hours',
	'userPaymentHistory': domain.url + 'Pricing/v1/payment/information',
	'orderTransactionDetails': domain.url + 'Pricing/v1/orders/transaction/details',
	'getUserPicture': domain.url + 'AAA/v1/user/profile/picture',

	'contactPagination': domain.url + 'AAA/v1/contact/find-with-pagination',
	'appTourFetch': domain.url + 'AAA/v1/app-tour/fetch',
	'appTourCreate': domain.url + 'AAA/v1/app-tour/create',
	'appTourUpdate': domain.url + 'AAA/v1/app-tour/update',

	'getWebhookKey': domain.url + 'AAA/v1/user-webhook-key/fetch',
	'createWebhookKey': domain.url + 'AAA/v1/user-webhook-key/create',
	'updateWebhookKey': domain.url + 'AAA/v1/user-webhook-key/update',

	'esContactFilter': domain.url + 'Search/v1/elastic/fetch/search-by-contact',
	// 'esCompanyFilter': domain.url + 'Search/v1/elastic/fetch/search-by-company',
	// 'esAdvancedSearchFilter': domain.url + 'Search/v1/elastic/fetch/advance-filter',
	'esAutoSuggestions': domain.url + 'Search/v1/elastic/suggestion/es-search-pattern',

	'esAdvancedSearchFilterCount': domain.url + 'Search/v1/elastic/fetch/advance-count',
	'esAdvancedSearchFilter': domain.url + 'Search/v1/elastic/fetch/advance-filter-data',
	'esAdvancedFilterContacts': domain.url + 'Search/v1/elastic/fetch/advance-filter-contacts',
	// 'esContactFilter': domain.url + 'Search/records/filter/contacts/search',
	// 'esCompanyFilter': domain.url + 'Search/records/fitler/companies/search',
	'removeProfilePicture': domain.url + 'AAA/v1/user/profile/picture/remove',

	'downloadAllCompanyData': domain.url + 'Search/v1/elastic/download-all/search-by-company',
	'downloadContactData': domain.url + 'dashboard/list/download',

	'esCompanyFilter': domain.url + 'Search/v1/elastic/fetch/company-data',
	'esAdvancedCompanySearchFilter': domain.url + 'Search/v1/elastic/fetch/company/advance-filter',
	'fetchSingleCompanyDetails': domain.url + 'Search/v1/elastic/company/view-details',
	'createCompanyWishList': domain.url + 'Search/v1/elastic/company/create-list',
	'downloadCompanyWishList': domain.url + 'Search/v1/elastic/company/download-list',
	'viewCompanyListItems': domain.url + 'Search/v1/elastic/company/filter/list-items',
	'cachedContactData': domain.url+'Search/v1/elastic/async/fetch/search-by-contact',
	'cachedCompanyData': domain.url+'Search/v1/elastic/async/fetch/search-by-company',
	'processWithWishlistId': domain.url+'Search/v1/emailverifier/history/proccess-wish-list-id',
	'getAllUsersCount': domain.url + 'AAA/v1/admin/count/get-all-users-plans',
	'listItemCreateV2': domain.url + 'AAA/v2/wish-list-items/create',
	'unverifiedAccounts': domain.url + 'AAA/v1/admin/get-email-verification-user-status',
	'ordersRegistration': domain.url + 'orders/registration',
	'loginAsCustomer': domain.url + 'AAA/v1/auth/login',
	'deactivateSubscriptions': domain.url + 'AAA/v1/user/account/cancel/subscription',
	'dataEnrich': domain.url + 'Search/v1/file/enrichment/upload',
	'fetchAllEnrich': domain.url + 'Search/v1/file/enrichment/fetchAll',
	'deleteEnrichment': domain.url + 'Search/v1/file/enrichment/delete-by-id',
	'downloadEnrichedFile': domain.url + 'enhanced-data/v1/file/enrichment/download',
	'transactionAmount': domain.url + 'Pricing/v1/orders/transaction/amount',
	'changePassword': domain.url + 'AAA/v1/auth/change-password',
	'transactionHistory': domain.url + 'Pricing/v1/orders/transaction/details'
}