import React, { useState, useEffect } from "react";
import { PostWithTokenNoCache } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";
import UseTabStore from "../common-files/useGlobalState";
import Alert from "../common-files/alert";

const SecurityTab = () => {
    const [currentPassword, setCurrentPassword] = useState("");
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [logoutAllDevices, setLogoutAllDevices] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    // Real-time validation states
    const [currentPasswordError, setCurrentPasswordError] = useState("");
    const [newPasswordError, setNewPasswordError] = useState("");
    const [confirmPasswordError, setConfirmPasswordError] = useState("");
    const [passwordSuccessMessage, setPasswordSuccessMessage] = useState("");

    const {
        setButtonType,
        setDefaultErrorMsg,
        setDefaultAlert,
        defaultAlert,
        defaultErrorMsg
    } = UseTabStore();

    // Get user email from localStorage - matching pattern used in ApplyFilter.js
    const getUserEmail = () => {
        try {
            const user = JSON.parse(localStorage.getItem('user'));
            if (user) {
                return user.email || '';
            }
        } catch (error) {
            // Error handled silently
        }
        return '';
    };



    // Real-time validation functions
    const validateCurrentPassword = (password) => {
        if (!password.trim()) {
            setCurrentPasswordError("Current password is required");
            return false;
        }
        setCurrentPasswordError("");
        return true;
    };

    const validateNewPassword = (password, skipSamePasswordCheck = false) => {
        if (!password.trim()) {
            setNewPasswordError("New password is required");
            return false;
        }

        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%&*()+=^.-])[A-Za-z\d!@#$%&*()+=^.-]{8,}$/;
        if (!passwordRegex.test(password)) {
            setNewPasswordError("Password must be at least 8 characters long, should contain at least one number, one upper case & lower case letter, one special character which includes !@#$%&*()-+=^.");
            return false;
        }

        // Real-time validation: Check if new password is same as current password
        // Skip this check if explicitly requested (for immediate error clearing)
        if (!skipSamePasswordCheck && currentPassword && password === currentPassword && password.trim() !== "") {
            setNewPasswordError("New password must be different from your current password");
            return false;
        }

        setNewPasswordError("");
        return true;
    };

    const validateConfirmPassword = (confirmPwd, newPwd) => {
        if (!confirmPwd.trim()) {
            setConfirmPasswordError("Confirm password is required");
            return false;
        }

        if (confirmPwd !== newPwd) {
            setConfirmPasswordError("New password and confirm password do not match");
            return false;
        }

        setConfirmPasswordError("");
        return true;
    };

    // Handle input changes with real-time validation
    const handleCurrentPasswordChange = (e) => {
        const value = e.target.value;
        setCurrentPassword(value);
        validateCurrentPassword(value);
        // Clear success message when user starts typing
        if (passwordSuccessMessage) {
            setPasswordSuccessMessage("");
        }
        // Re-validate new password to check for same password scenario
        if (newPassword) {
            validateNewPassword(newPassword);
        }
    };

    const handleNewPasswordChange = (e) => {
        const value = e.target.value;
        setNewPassword(value);

        // Clear success message when user starts typing
        if (passwordSuccessMessage) {
            setPasswordSuccessMessage("");
        }

        // Clear the "same password" error immediately when user starts typing
        if (newPasswordError === "New password must be different from your current password") {
            setNewPasswordError("");
            // Validate without the same password check initially to allow immediate clearing
            setTimeout(() => {
                validateNewPassword(value);
            }, 100);
        } else {
            validateNewPassword(value);
        }

        // Re-validate confirm password if it exists
        if (confirmPassword) {
            validateConfirmPassword(confirmPassword, value);
        }
    };

    const handleConfirmPasswordChange = (e) => {
        const value = e.target.value;
        setConfirmPassword(value);
        // Clear success message when user starts typing
        if (passwordSuccessMessage) {
            setPasswordSuccessMessage("");
        }
        validateConfirmPassword(value, newPassword);
    };

    // Form validation for submission
    const validateForm = () => {
        const isCurrentPasswordValid = validateCurrentPassword(currentPassword);
        const isNewPasswordValid = validateNewPassword(newPassword);
        const isConfirmPasswordValid = validateConfirmPassword(confirmPassword, newPassword);

        return isCurrentPasswordValid && isNewPasswordValid && isConfirmPasswordValid;
    };

    // Handle password change
    const handlePasswordChange = async () => {
        if (!validateForm()) {
            return;
        }

        setIsLoading(true);
        const email = getUserEmail();

        if (!email) {
            setButtonType("error");
            setDefaultErrorMsg("Enter a valid email address");
            setDefaultAlert(true);
            setIsLoading(false);
            return;
        }

        const params = {
            email: email,
            currentPassword: currentPassword,
            newPassword: newPassword,
            confirmPassword: confirmPassword,
            logOutAllDevices: logoutAllDevices
        };

        try {
            const response = await PostWithTokenNoCache(ApiName.changePassword, params);

            if (response && response.data) {
                if (response.data.status === 200) {
                    // Handle success response with inline message
                    setPasswordSuccessMessage(response?.data?.message || "Password changed successfully!");

                    // Clear form and error messages
                    setCurrentPassword("");
                    setNewPassword("");
                    setConfirmPassword("");
                    setLogoutAllDevices(false); // Reset to unchecked state
                    setCurrentPasswordError("");
                    setNewPasswordError("");
                    setConfirmPasswordError("");

                    // Ensure modal alert is not triggered
                    setDefaultAlert(false);
                    setDefaultErrorMsg("");
                    setButtonType("");

                    // Clear success message after 5 seconds
                    setTimeout(() => {
                        setPasswordSuccessMessage("");
                    }, 5000);
                }
            }

        } catch (error) {
            // Handle network errors and other exceptions
            const status = error.response.status;
            const message = error?.response?.data?.message || "An error occurred";

            if (status === 400) {
                // Handle client errors
                const errorMessage = message || "Enter a valid password";
                setCurrentPasswordError(errorMessage);
                setButtonType("error");
                setDefaultErrorMsg(message || "Network error occurred. Please try again.");
                setDefaultAlert(true);
            } else {
                setButtonType("error");
                setDefaultErrorMsg(message || "Network error occurred. Please try again.");
                setDefaultAlert(true);
            }
        } finally {
            setIsLoading(false);
        }
    };
    return (
        <>
            <style>
                {`
                    /* Comprehensive visibility fix for password input types only */
                    .my-security input[type="text"] {
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        height: auto !important;
                        min-height: 40px !important;
                        width: 100% !important;
                        position: relative !important;
                        z-index: 1 !important;
                        background: transparent !important;
                        border: none !important;
                        border-bottom: 1px solid #ECECEC !important;
                        outline: none !important;
                        padding: 0px 0 10px 0px !important;
                        font-size: 14px !important;
                        font-family: 'poppins' !important;
                        transition: border-color 0.3s ease !important;
                        margin: 0 0 0px 0 !important;
                    }

                    /* Specific styling for checkboxes to ensure visibility */
                    .my-security input[type="checkbox"] {
                        display: inline-block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        width: 16px !important;
                        height: 16px !important;
                        margin: 0 8px 0 0 !important;
                        padding: 0 !important;
                        border: 1px solid #BFBFBF !important;
                        border-radius: 2px !important;
                        background: #fff !important;
                        cursor: pointer !important;
                        position: relative !important;
                        z-index: 1 !important;
                        appearance: none !important;
                        -webkit-appearance: none !important;
                        -moz-appearance: none !important;
                    }

                    /* Checkbox checked state */
                    .my-security input[type="checkbox"]:checked {
                        background-color: #55c2c3 !important;
                        border-color: #55c2c3 !important;
                    }

                    /* Checkbox checkmark */
                    .my-security input[type="checkbox"]:checked::before {
                        content: "✓" !important;
                        display: block !important;
                        color: white !important;
                        font-size: 12px !important;
                        text-align: center !important;
                        line-height: 14px !important;
                        position: absolute !important;
                        top: 0 !important;
                        left: 0 !important;
                        width: 100% !important;
                        height: 100% !important;
                    }

                    /* Custom checkbox label styling */
                    .my-security .custom-checkbox {
                        display: flex !important;
                        align-items: center !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        margin: 15px 0 !important;
                    }

                    .my-security .i-agree {
                        font-size: 13px !important;
                        font-weight: 500 !important;
                        font-family: 'Poppins', sans-serif !important;
                        color: #333 !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        cursor: pointer !important;
                    }

                    .my-security .input-wrapper-2 {
                        display: flex !important;
                        flex-direction: column !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        margin-bottom: 20px !important;
                    }

                    .my-security .row {
                        display: flex !important;
                        visibility: visible !important;
                        margin-bottom: 15px !important;
                    }

                    .my-security .col-md-6 {
                        display: block !important;
                        visibility: visible !important;
                        flex: 0 0 50% !important;
                        max-width: 50% !important;
                    }

                    .my-security .col-md-4 {
                        display: block !important;
                        visibility: visible !important;
                        flex: 0 0 33.333333% !important;
                        max-width: 33.333333% !important;
                    }

                    /* My-last-step-2 container styling - restored to original layout */
                    .my-security .my-last-step-2 {
                        visibility: visible !important;
                        opacity: 1 !important;
                        /* Remove flex layout to restore original positioning */
                    }

                    /* Override any browser password manager hiding */
                    .my-security input:-webkit-autofill {
                        -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
                        -webkit-text-fill-color: inherit !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                    }
                `}
            </style>
            <div className="profile-box-2">
                <div className="row">
                    <div className="col-md-2"></div>
                    <div className="col-md-8">
                        <div className="profile-box">

                            <div className="d-flex flex-row justify-content-between">
                                <div>
                                    <p className="change-password">Change Passsword</p>
                                </div>
                            </div>
                            <div className="my-security" style={{ display: 'block !important', visibility: 'visible !important' }}>
                                <div className="row">
                                    <div className="col-md-4">
                                        <div className="input-wrapper-2">
                                            <label htmlFor="currentPassword">Your Current Password</label>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="input-wrapper-2">
                                            <input
                                                type="text"
                                                id="currentPassword"
                                                name="currentPassword"
                                                className="input-bottom-border-2"
                                                placeholder="Enter your current password"
                                                value={currentPassword}
                                                onChange={handleCurrentPasswordChange}
                                                autoComplete="off"
                                            />
                                            {currentPasswordError && (
                                                <div style={{ color: 'red', fontSize: '14px', marginTop: '5px' }}>
                                                    {currentPasswordError}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="row">
                                    <div className="col-md-4">
                                        <div className="input-wrapper-2">
                                            <label htmlFor="newPassword">New Password</label>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="input-wrapper-2">
                                            <input
                                                type="text"
                                                id="newPassword"
                                                name="newPassword"
                                                className="input-bottom-border-2"
                                                placeholder="Enter your new password"
                                                value={newPassword}
                                                onChange={handleNewPasswordChange}
                                                autoComplete="off"
                                            />
                                            {newPasswordError && (
                                                <div style={{ color: 'red', fontSize: '14px', marginTop: '5px' }}>
                                                    {newPasswordError}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="row">
                                    <div className="col-md-4">
                                        <div className="input-wrapper-2">
                                            <label htmlFor="confirmPassword">Confirm New Password</label>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="input-wrapper-2">
                                            <input
                                                type="text"
                                                id="confirmPassword"
                                                name="confirmPassword"
                                                className="input-bottom-border-2"
                                                placeholder="Enter your confirmed new password"
                                                value={confirmPassword}
                                                onChange={handleConfirmPasswordChange}
                                                autoComplete="off"
                                            />
                                            {confirmPasswordError && (
                                                <div style={{ color: 'red', fontSize: '14px', marginTop: '5px' }}>
                                                    {confirmPasswordError}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="my-last-step-2">
                                    <div>
                                        <label className="custom-checkbox">
                                            <input
                                                type="checkbox"
                                                name="logoutAllDevices"
                                                checked={logoutAllDevices}
                                                onChange={(e) => setLogoutAllDevices(e.target.checked)}
                                            />
                                            <span className="i-agree">Log me out of all device</span>
                                        </label>
                                    </div>
                                    <button
                                        type="button"
                                        className="save-out"
                                        disabled={isLoading}
                                        onClick={handlePasswordChange}
                                    >
                                        {isLoading ? "Saving..." : "Save"}
                                    </button>
                                    {passwordSuccessMessage && (
                                        <div style={{ color: '#28a745', fontSize: '14px', marginTop: '10px', fontWeight: '500' }}>
                                            {passwordSuccessMessage}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="col-md-2"></div>
                </div>


                <div className="row">
                    <div className="col-md-2"></div>
                    <div className="col-md-8">
                        <div className="profile-box mt-3">

                            <div className="d-flex flex-row justify-content-between">
                                <div>
                                    {/* <p className="change-password">Recent security activity</p>
                                <p className="security-activity">Security activity and alerts from the last 60 days.</p> */}
                                </div>
                            </div>
                            {/* <div className="my-security">

                            <div className="row">
                                <div className="col-md-4">
                                    <p className="my-date">March 26, 2025</p>
                                </div>
                                <div className="col-md-6">
                                    <p className="current-session">Windows Chrome <span>(Current session) IN Benglore (106.222.202.47)</span></p>
                                </div>
                            </div>

                            <div className="row">
                                <div className="col-md-4">
                                    <p className="my-date">March 26, 2025</p>
                                </div>
                                <div className="col-md-6">
                                    <p className="current-session">Windows Chrome <span>(Current session) IN Benglore (106.222.202.47)</span></p>
                                </div>
                            </div>

                            <div className="row">
                                <div className="col-md-4">
                                    <p className="my-date">March 26, 2025</p>
                                </div>
                                <div className="col-md-6">
                                    <p className="current-session">Windows Chrome <span>(Current session) IN Benglore (106.222.202.47)</span></p>
                                </div>
                            </div>
                        </div> */}
                        </div>
                    </div>
                    <div className="col-md-2"></div>
                </div>
                {/* Alert component for success/error messages */}
                {defaultAlert && defaultErrorMsg && (
                    <Alert data={defaultErrorMsg} />
                )}

            </div>
        </>
    );
};

export default SecurityTab;
