import React, { useState, useEffect } from "react";
import { format } from 'date-fns';
import { PostWithTokenNoCache } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";



const SubscriptionTab = ({ userCredits }) => {
    console.log("userCredits", userCredits);

    // State for transaction history
    const [transactionHistory, setTransactionHistory] = useState([]);
    const [isLoadingTransactions, setIsLoadingTransactions] = useState(false);
    const [transactionError, setTransactionError] = useState("");

    // Fetch transaction history
    const fetchTransactionHistory = async () => {
        console.log("🔄 fetchTransactionHistory: Function called - starting transaction history fetch");

        setIsLoadingTransactions(true);
        setTransactionError("");

        const params = {
            page: 1,
            pageSize: 7,
            sortBy: "desc",
            searchParams: {
                transaction_status: "paid"
            }
        };

        console.log("📤 fetchTransactionHistory: Request parameters being sent to API:", params);
        console.log("🌐 fetchTransactionHistory: API endpoint:", ApiName.transactionHistory);

        try {
            const response = await PostWithTokenNoCache(ApiName.transactionHistory, params);

            console.log("📥 fetchTransactionHistory: Raw API response received:", response);
            console.log("📊 fetchTransactionHistory: Response data:", response?.data);
            console.log("✅ fetchTransactionHistory: Response status:", response?.data?.status);

            if (response && response.data) {
                // Check if response has status field, if not assume success (200 status from HTTP)
                const hasStatusField = 'status' in response.data;
                const isSuccess = hasStatusField ? response.data.status === 200 : response.status === 200;

                console.log("🔍 fetchTransactionHistory: Has status field in data?", hasStatusField);
                console.log("🔍 fetchTransactionHistory: Response data status:", response.data.status);
                console.log("🔍 fetchTransactionHistory: HTTP status:", response.status);
                console.log("🔍 fetchTransactionHistory: Is success?", isSuccess);

                if (isSuccess) {
                    // Handle success response - check both possible data locations
                    console.log("🔍 fetchTransactionHistory: Full response.data:", response.data);

                    // Try response.data.data first (nested data), then response.data directly
                    let rawTransactionData = response.data.data;

                    // If response.data.data doesn't exist or is null, use response.data directly
                    if (!rawTransactionData) {
                        console.log("🔄 fetchTransactionHistory: response.data.data is null/undefined, using response.data directly");
                        rawTransactionData = response.data;
                    }

                    console.log("🎯 fetchTransactionHistory: Raw transaction data from API:", rawTransactionData);
                    console.log("🔍 fetchTransactionHistory: Data type:", typeof rawTransactionData);
                    console.log("🔍 fetchTransactionHistory: Is array:", Array.isArray(rawTransactionData));

                    // Handle different possible data structures
                    let transactionData = [];

                    console.log("🔍 fetchTransactionHistory: Starting data structure analysis...");
                    console.log("🔍 fetchTransactionHistory: rawTransactionData exists?", !!rawTransactionData);
                    console.log("🔍 fetchTransactionHistory: rawTransactionData type:", typeof rawTransactionData);
                    console.log("🔍 fetchTransactionHistory: rawTransactionData is array?", Array.isArray(rawTransactionData));

                    if (Array.isArray(rawTransactionData)) {
                        // Data is already an array
                        transactionData = rawTransactionData;
                        console.log("✅ fetchTransactionHistory: Data is already an array");
                    } else if (rawTransactionData && typeof rawTransactionData === 'object') {
                        console.log("🔍 fetchTransactionHistory: Data is an object, entering object exploration...");
                        // Data might be an object with transactions property
                        console.log("🔍 fetchTransactionHistory: Exploring object structure...");
                        console.log("🔍 fetchTransactionHistory: Object keys:", Object.keys(rawTransactionData));
                        console.log("🔍 fetchTransactionHistory: Object values:", Object.values(rawTransactionData));
                        console.log("🔍 fetchTransactionHistory: Checking for items property:", rawTransactionData.items);
                        console.log("🔍 fetchTransactionHistory: Is items an array?", Array.isArray(rawTransactionData.items));

                        if (Array.isArray(rawTransactionData.transactions)) {
                            transactionData = rawTransactionData.transactions;
                            console.log("✅ fetchTransactionHistory: Found transactions array in data object");
                        } else if (Array.isArray(rawTransactionData.data)) {
                            transactionData = rawTransactionData.data;
                            console.log("✅ fetchTransactionHistory: Found data array in data object");
                        } else if (Array.isArray(rawTransactionData.items)) {
                            transactionData = rawTransactionData.items;
                            console.log("✅ fetchTransactionHistory: Found items array in data object");
                            console.log("✅ fetchTransactionHistory: Items array length:", rawTransactionData.items.length);
                        } else if (Array.isArray(rawTransactionData.records)) {
                            transactionData = rawTransactionData.records;
                            console.log("✅ fetchTransactionHistory: Found records array in data object");
                        } else if (Array.isArray(rawTransactionData.results)) {
                            transactionData = rawTransactionData.results;
                            console.log("✅ fetchTransactionHistory: Found results array in data object");
                        } else if (Array.isArray(rawTransactionData.list)) {
                            transactionData = rawTransactionData.list;
                            console.log("✅ fetchTransactionHistory: Found list array in data object");
                        } else {
                            // Check if any property contains an array
                            const arrayProperty = Object.keys(rawTransactionData).find(key =>
                                Array.isArray(rawTransactionData[key])
                            );

                            if (arrayProperty) {
                                transactionData = rawTransactionData[arrayProperty];
                                console.log(`✅ fetchTransactionHistory: Found array in property '${arrayProperty}'`);
                            } else {
                                // Single transaction object, wrap in array
                                transactionData = [rawTransactionData];
                                console.log("✅ fetchTransactionHistory: Single transaction object, wrapped in array");
                            }
                        }
                    } else {
                        // Final fallback - check directly for items property
                        console.warn("⚠️ fetchTransactionHistory: Reached fallback section");
                        console.warn("⚠️ fetchTransactionHistory: Raw data was:", rawTransactionData);

                        if (rawTransactionData && rawTransactionData.items && Array.isArray(rawTransactionData.items)) {
                            console.log("🔧 fetchTransactionHistory: Direct fallback found items array!");
                            transactionData = rawTransactionData.items;
                        } else {
                            console.warn("⚠️ fetchTransactionHistory: Could not parse transaction data, using empty array");
                            transactionData = [];
                        }
                    }

                    console.log("📈 fetchTransactionHistory: Final transaction data:", transactionData);
                    console.log("📈 fetchTransactionHistory: Number of transactions:", transactionData.length);

                    // Transform API data to UI format
                    const transformedTransactions = Array.isArray(transactionData) ? transactionData.map((apiTransaction, index) => {
                        console.log(`🔄 fetchTransactionHistory: Transforming transaction ${index + 1}:`, apiTransaction);

                        const transformedTransaction = {
                            // Map API fields to UI expected fields
                            invoice_date: apiTransaction.transaction_createdAt || apiTransaction.created_at || apiTransaction.date,
                            invoice_id: apiTransaction.transaction_id || apiTransaction.id || apiTransaction.invoice_id,
                            status: apiTransaction.transaction_status || apiTransaction.status,
                            amount: apiTransaction.transaction_amount || apiTransaction.amount,
                            // Keep original data for debugging
                            _original: apiTransaction
                        };

                        console.log(`✨ fetchTransactionHistory: Transformed transaction ${index + 1}:`, transformedTransaction);

                        // Verify required fields are present in transformed data
                        const missingFields = [];
                        if (!transformedTransaction.invoice_date) missingFields.push('invoice_date');
                        if (!transformedTransaction.invoice_id) missingFields.push('invoice_id');
                        if (!transformedTransaction.status) missingFields.push('status');
                        if (!transformedTransaction.amount) missingFields.push('amount');

                        if (missingFields.length > 0) {
                            console.warn(`⚠️ fetchTransactionHistory: Transformed transaction ${index + 1} missing fields:`, missingFields);
                        } else {
                            console.log(`✅ fetchTransactionHistory: Transformed transaction ${index + 1} has all required fields`);
                        }

                        return transformedTransaction;
                    }) : [];

                    console.log("🎯 fetchTransactionHistory: All transformed transactions:", transformedTransactions);
                    console.log("📊 fetchTransactionHistory: Number of transformed transactions:", transformedTransactions.length);

                    setTransactionHistory(transformedTransactions);
                    setTransactionError("");
                    console.log("✅ fetchTransactionHistory: Transaction history state updated successfully");
                } else {
                    console.warn("⚠️ fetchTransactionHistory: API returned non-200 status:", response.data.status);
                    console.warn("⚠️ fetchTransactionHistory: API response message:", response.data.message);
                }
            } else {
                console.warn("⚠️ fetchTransactionHistory: Invalid response structure received");
            }
        } catch (error) {
            // Handle network errors and other exceptions
            console.error("❌ fetchTransactionHistory: Error caught in catch block");
            console.error("❌ fetchTransactionHistory: Error object:", error);
            console.error("❌ fetchTransactionHistory: Error response:", error?.response);
            console.error("❌ fetchTransactionHistory: Error response data:", error?.response?.data);
            console.error("❌ fetchTransactionHistory: Error response status:", error?.response?.status);
            console.error("❌ fetchTransactionHistory: Error message:", error?.response?.data?.message);

            const message = error?.response?.data?.message || "Failed to fetch transaction history";
            setTransactionError(message);
            console.error("❌ fetchTransactionHistory: Final error message set:", message);
        } finally {
            setIsLoadingTransactions(false);
            console.log("🏁 fetchTransactionHistory: Function completed - loading state set to false");
        }
    };

    // Fetch transaction history on component mount
    useEffect(() => {
        fetchTransactionHistory();
    }, []);

    // Helper function to format credit values
    const formatCreditValue = (value) => {
        if (value === null || value === undefined) return "0";
        if (typeof value === "number") return value.toLocaleString("en-IN");
        if (typeof value === "string" && value.toLowerCase() === "unlimited")
            return "Unlimited";
        return value;
    };

    // Helper function to format transaction date
    const formatTransactionDate = (dateString) => {
        if (!dateString) return "N/A";
        try {
            // Handle ISO date format from API (e.g., "2025-07-31T04:40:33.621+00:00")
            const date = new Date(dateString);
            return format(date, 'M/d/yyyy');
        } catch (error) {
            console.warn("⚠️ formatTransactionDate: Error formatting date:", dateString, error);
            return dateString;
        }
    };

    // Helper function to format amount
    const formatAmount = (amount) => {
        if (!amount) return "–";
        try {
            // Handle string amounts from API (e.g., "58800" represents $588.00)
            let numericAmount;

            if (typeof amount === 'string') {
                // Convert string to number and divide by 100 to get dollars from cents
                numericAmount = parseFloat(amount) / 100;
                console.log(`💰 formatAmount: Converting string amount "${amount}" to $${numericAmount.toFixed(2)}`);
            } else if (typeof amount === 'number') {
                // If already a number, check if it needs to be divided by 100
                // Assume amounts over 1000 are in cents
                numericAmount = amount > 1000 ? amount / 100 : amount;
                console.log(`💰 formatAmount: Converting number amount ${amount} to $${numericAmount.toFixed(2)}`);
            } else {
                console.warn("⚠️ formatAmount: Unknown amount format:", amount);
                return "–";
            }

            return `$${numericAmount.toFixed(2)}`;
        } catch (error) {
            console.warn("⚠️ formatAmount: Error formatting amount:", amount, error);
            return "–";
        }
    };

    // Parse the plan name to make it more readable
    const parsePlanName = (planName) => {
        if (!planName) return "Free";
        return planName
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    };

    // Format the period end date
    const formatDate = (dateString) => {
        if (!dateString) return "N/A";
        try {
            return format(new Date(dateString), 'MMM d, yyyy');
        } catch {
            return dateString;
        }
    };

    // Determine if the plan is yearly based on package_name and billing_period
    const isYearlyPlan = () => {
        if (!userCredits || !userCredits.user_plan_name) return false;

        // Check if plan name contains "yearly" (case insensitive)
        const isYearlyInName = userCredits.user_plan_name.toLowerCase().includes('yearly');

        // If billing_period is available in userCredits, use that
        if (userCredits.billing_period) {
            return userCredits.billing_period.toLowerCase() === 'yearly';
        }

        // Fallback to checking the plan name
        return isYearlyInName;
    };

    return (
        <div className="profile-box-2">
            <div className="row">
                <div className="col-md-2"></div>
                <div className="col-md-8">
                    <div className="profile-box">
                        <div className="my-sub">
                            <div className="d-flex flex-row justify-content-between">
                                <div>
                                    <p className="my-second-subscription">Subscription</p>
                                </div>
                                <div>
                                    <button type="button" className="my-upgrade">Upgrade</button>
                                </div>
                            </div>

                            <div className="sub-table-border">
                                <div className="current-plan">
                                    <p className="my-current plan">Current Plan</p>
                                </div>
                                <div className="row">
                                    <div className="col-md-6">
                                        <p className="my-freemium">
                                            {userCredits ? parsePlanName(userCredits.user_plan_name) : "Loading..."}
                                        </p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="check-my-views">
                                            <img src="images/account-credits-view-icon.png" alt="Views" />
                                            {userCredits
                                                ? userCredits.total_assigned_contact_view === "Unlimited"
                                                    ? "Unlimited Profile Views"
                                                    : `${formatCreditValue(userCredits.total_balance_contact_view)}/${formatCreditValue(userCredits.total_assigned_contact_view)} Profile Views`
                                                : "Loading..."}
                                        </p>
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="col-md-6">
                                        <p className="my-freemium">
                                            {userCredits?.period_end
                                                ? `Renews on ${formatDate(userCredits.period_end)}`
                                                : "Loading..."}
                                        </p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="check-my-views">
                                            <img src="images/accounts-credits-download.png" alt="Downloads" />
                                            {userCredits
                                                ? `${formatCreditValue(userCredits.total_assigned_credit)} ${isYearlyPlan() ? 'Yearly' : 'Monthly'} Downloads`
                                                : "Loading..."}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="col-md-2"></div>
            </div>


            <div className="row">
                <div className="col-md-2"></div>
                <div className="col-md-8">
                    <div className="profile-box mt-3">
                        <div className="my-sub">
                            <div className="d-flex flex-row justify-content-between">
                                <div>
                                    <p className="my-second-subscription">Credit Card Information</p>
                                </div>
                                <div>
                                    <button type="button" className="my-upgrade">Update Credit Card</button>
                                </div>
                            </div>

                            <div className="row">
                                <div className="col-md-7">
                                    <div className="sub-table-border">
                                        <div className="current-plan">
                                            <p className="my-current plan">Card Information</p>
                                        </div>
                                        <div className="row">
                                            <div className="col-md-12">
                                                <p className="my-freemium">
                                                    Card Number: **** **** **** 4800<br />
                                                    Expiration Date: 3/2028
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-md-5">
                                    <p className="my-billing">Billing Address on Card</p>
                                    <p className="my-moqdom">
                                        Maqdoom Syed<br />
                                        3080, Olcott St, Ste B220<br />
                                        Santa Clara, California 95054<br />
                                        US
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="col-md-2"></div>
            </div>

            <div className="row">
                <div className="col-md-2"></div>
                <div className="col-md-8">
                    <div className="profile-box mt-3">
                        <div className="my-sub">
                            <div className="invoice-container">
                                <div className="invoice-vertical-scroll">
                                    <table className="invoice-table">
                                        <thead>
                                            <tr>
                                                <th>Invoice Date</th>
                                                <th>Invoice ID</th>
                                                <th>Status</th>
                                                <th>Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {isLoadingTransactions ? (
                                                <tr>
                                                    <td colSpan="4" style={{ textAlign: 'center', padding: '20px' }}>
                                                        Loading transaction history...
                                                    </td>
                                                </tr>
                                            ) : transactionError ? (
                                                <tr>
                                                    <td colSpan="4" style={{ textAlign: 'center', padding: '20px', color: 'red' }}>
                                                        {transactionError}
                                                    </td>
                                                </tr>
                                            ) : transactionHistory.length === 0 ? (
                                                <tr>
                                                    <td colSpan="4" style={{ textAlign: 'center', padding: '20px' }}>
                                                        No transaction history found
                                                    </td>
                                                </tr>
                                            ) : (
                                                transactionHistory.map((transaction, idx) => (
                                                    <tr key={transaction.invoice_id || idx}>
                                                        <td>{formatTransactionDate(transaction.invoice_date)}</td>
                                                        <td>{transaction.invoice_id || 'N/A'}</td>
                                                        <td className="status paid">{transaction.status || 'Paid'}</td>
                                                        <td>{formatAmount(transaction.amount)}</td>
                                                    </tr>
                                                ))
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div className="col-md-2"></div>
            </div>

        </div>
    );
};

export default SubscriptionTab;