@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap');


body {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  /* Regular */
}

.tabs-header {
  margin: 0;
  padding: 0;
  border-bottom: 1px solid rgb(221, 221, 221);
}

.tab-btn {
  position: relative;
  overflow: hidden;
  color: #333;
  padding: 10px 20px 18px;
  cursor: pointer;
  transition: color 0.5s ease, border-color 0.5s ease;
  font-size: 12px;
  z-index: 1;
  border-radius: 6px;
}

.tab-btn::before {
  content: "";
  position: absolute;
  top: 3px;
  left: 0;
  right: 0;
  bottom: 11px;
  background-color: #edecec;
  z-index: 0;
  opacity: 0;
  transition: opacity 0.4s ease;
  border-radius: 6px;
  box-shadow: inset 0px 0px 4px 1px rgba(0, 0, 0, 0.08);
}

.tab-btn:hover {
  color: #000;
}

.tab-btn:hover::before {
  opacity: 1;
}

.tab-btn>* {
  position: relative;
  z-index: 2;
}

.tab-btn span,
.tab-btn i {
  font-family: 'Poppins', sans-serif;
}

/* ✅ Active tab underline */
.tab-btn.active::after {
  content: '';
  position: absolute;
  left: 0px;
  right: 0;
  bottom: 0px;
  height: 3px;
  background-color: #007bff;
  border-radius: 57px;
  z-index: 3;
  transform: scaleX(1);
  transition: transform 0.6s ease-in-out;
}

/* Optional: Animate underline in when active tab changes */
.tab-btn::after {
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.3s ease-in-out;
}

.tab-btn.active::after {
  transform: scaleX(1);
}






.scrollable-panel {
  height: 100vh;
  overflow-y: auto;
  padding-top: 20px;
  border-right: 1px solid #000;
  scroll-behavior: smooth;
}

.profile-box {
  border-radius: 8px;
  background-color: #fff;
  /* width: 540px; */
  margin: auto;
}


p.account-information {
  position: relative;
  margin: 4rem 0 0 3rem;
  font-family: poppins;
  color: #151417;
  font-weight: 500;
  font-size: 18px;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.input-wrapper-2 {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.input-wrapper-2::placeholder {
  font-size: 14px;
}

.input-wrapper label {
  font-size: 12px;
  color: #8B8B8B;
  margin-bottom: 0;
  font-weight: 400;
  font-family: 'poppins';
}

.input-wrapper-2 label {
  font-size: 12px;
  color: #8B8B8B;
  margin-bottom: 0;
  font-weight: 400;
  font-family: 'poppins';
}

.input-bottom-border-2::placeholder {
  color: #a8a8a8b7;
  font-size: 12px;
  font-family: 'Poppins', sans-serif;

}

.input-bottom-border-2 {
  border: none;
  border-bottom: 1px solid #ECECEC;
  outline: none;
  padding: 0px 0 10px 0px;
  font-size: 12px;
  width: 100%;
  background: transparent;
  transition: border-color 0.3s ease;
  margin: 0 0 0px 0;
  font-family: 'poppins';
  color: #979696;
}

.input-bottom-border {
  border: none;
  border-bottom: 1px solid #ECECEC;
  outline: none;
  padding: 6px 0;
  font-size: 14px;
  width: 100%;
  background: transparent;
  transition: border-color 0.6s ease;
}

.input-bottom-border::placeholder {
  color: #c1c1c18c;
  font-size: 12px;
  font-family: 'poppins';
}


.input-wrapper {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.label-with-icon {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 0px;
  justify-content: space-between;
}

.label-with-icon label {
  font-size: 14px;
  color: #8B8B8B;
  font-weight: 500;
}

.social-icon {
  box-shadow: 0 0 2px 1px #80808038;
  width: 23px;
  height: 23px;
  object-fit: contain;
  background-color: #d6d6d645;
  padding: 0 4px 0 4px;
  border-radius: 13px;
  font-family: 'poppins';

}



.input-bottom-border-two {
  border: none;
  border-bottom: 1px solid #c1c1c18c;
  outline: none;
  padding: 6px 0;
  font-size: 14px;
  width: 100%;
  background: transparent;
  transition: border-color 0.6s ease;
}

.input-bottom-border-two::placeholder {
  color: #8B8B8B;
  font-size: 12px;
  font-family: 'poppins';
}



.input-bottom-border:focus {
  border-bottom: 1px solid #115EA3;
}

.profile-box-2 {
  height: 80vh;
  overflow-y: auto;
  padding: 40px 20px 20px 20px;
  scroll-behavior: smooth;
}

/* Hide scrollbar for WebKit browsers (Chrome, Safari) */
.profile-box-2::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for Firefox */
.profile-box-2 {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}


h6.my-account-social-media {
  margin: 20px 0 20px 0;
  font-family: 'poppins';
  font-weight: 500;
  font-size: 18px;
}

.company-information {
  /* background-color: aqua; */
  width: 450px;
  margin: auto;
}






.my-last-step {
  /* width: 530px; */
  margin: 2rem auto 0 auto;
}

.my-last-step-2 {
  width: 530px;
  margin: 0px auto 0 auto;
}

span.i-agree {
  font-size: 13px;
  font-weight: 500;
  font-family: 'Poppins', sans-serif;
  margin: 0px;
  padding: 2px 0 0 0;
}

.update-and-save {
  background-color: #115EA3;
  color: white;
  padding: 7px 30px;
  border: none;
  border-radius: 35px;
  font-size: 12px;
  cursor: pointer;
  transition: opacity 0.7s ease, transform 0.5s ease;
  margin: 0px 0 0 0;
  font-family: 'poppins';
}

/* Fade out on hover */
.update-and-save:hover {
  opacity: 1;
  /* Fade in on hover */
  transform: scale(1.10);
  /* Optional subtle pop effect */
  background-color: #07457c;
}

.save-out {
  background-color: #115EA3;
  color: white;
  padding: 7px 30px;
  border: none;
  border-radius: 35px;
  font-size: 12px;
  cursor: pointer;
  transition: opacity 0.7s ease, transform 0.5s ease;
  margin: 6px 0 30px 0;
  font-family: 'poppins';
}

/* Fade out on hover */
.save-out:hover {
  opacity: 1;
  /* Fade in on hover */
  transform: scale(1.10);
  /* Optional subtle pop effect */
  background-color: #07457c;
}

p.change-password {
  margin: 20px 0 20px 30px;
  font-size: 14px;
  font-family: 'poppins';
}

.my-security {
  /* background-color: aqua; */
  width: 480px;
  margin: auto;
}

p.security-activity {
  margin: 0 0 0 0;
  font-size: 12px;
  padding: 0 0 20px 30px;
  color: #C8C6C4;
  font-family: 'poppins';
}

p.my-date {
  font-size: 12px;
  color: #000;
  font-family: 'poppins';
}

p.current-session {
  font-size: 12px;
  font-weight: 800;
  font-family: 'poppins';
}

p span.current-session {
  font-weight: 400;
}

.my-api-section {
  padding: 20px 45px;
}

.api-key-wrapper {
  position: relative;
  max-width: 400px;
  margin-top: 0;
}

.api-key-wrapper input {
  width: 300px;
  padding: 4px 100px 4px 12px;
  /* top/bottom: 10px, right: 100px (for icons), left: 12px */
  font-size: 16px;
  border: 1px solid #C9C9C9;
  border-radius: 6px;
  font-family: monospace;
  background-color: #F9F9F9;
  outline: none;
  box-sizing: border-box;
  font-family: 'poppins';
}

.api-key-wrapper input::placeholder {
  color: #146EF6;
  font-size: 14px;
}

.toggle-visibility,
.copy-api-key {
  position: absolute;
  top: 18px;
  transform: translateY(-50%);
  cursor: pointer;
  display: flex;
  align-items: center;
}

.toggle-visibility {
  right: 70px;
}

.copy-api-key {
  right: 14px;
  gap: 8px;
}

.copy-api-key .copy-text {
  font-size: 12px;
  color: #007bff;
  font-weight: 500;
}

/* Tooltip under "Copy" */
.copy-api-key .tooltip {
  position: absolute;
  top: 110%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #F9F9F9;
  color: #737373;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  animation: fadeIn 0.3s ease;
  z-index: 10;
  border: 1px solid #07C5D1;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(5px);
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}


p.my-api {
  font-size: 14px;
  color: #151417;
  font-family: 'poppins';
  font-weight: 500;
}

p.my-api-key {
  font-size: 12px;
  color: #8B8B8B;
  font-family: 'poppins';
  margin: 8px 0 0 0;

}


.generate-btn {
  margin: 20px 0 0 0px;
  border: 0;
  font-size: 14px;
  padding: 6px 25px;
  border-radius: 23px;
  outline: none;
  background-color: #115ea3;
  font-family: 'Poppins', sans-serif;
  color: #fff;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: background-color 0.3s ease;
}

.generate-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -75%;
  width: 50%;
  height: 100%;
  background: linear-gradient(120deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.6) 50%,
      rgba(255, 255, 255, 0.2) 100%);
  transform: skewX(-20deg);
}

.generate-btn:hover::before {
  animation: shine-sweep 0.9s ease forwards;
}

@keyframes shine-sweep {
  0% {
    left: -75%;
  }

  100% {
    left: 125%;
  }
}

.generate-btn span {
  display: inline-block;
  transition: transform 0.4s ease;
}

.generate-btn.flip span {
  transform: rotateX(180deg);
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  transform: scale(0);
  animation: ripple-animation 600ms linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}


.webhook-container {
  margin-top: 10px;
  padding: 0;
  border-radius: 10px;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}


input.my-input {
  width: 100%;
  box-sizing: border-box;
  border: 1px solid #C9C9C9;
  outline: none;
  border-radius: 4px;
  font-family: 'poppins';
  margin: 0 0 10px 0;
}

input.my-input::placeholder {
  font-size: 12px;
  color: #8B8B8B;
  padding: 0 0 0 10px;
}

p.my-url {
  margin: 9px 0 0 0;
  font-size: 12px;
  font-family: 'poppins';
}

p.my-active {
  margin: 2px 0px 0 4px;
  color: #115EA3;
  font-size: 12px;
  font-family: 'poppins';

}

.webhook-generate {
  background-color: #115EA3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.webhook-generate:hover {
  animation: pulse 0.6s ease;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(0, 123, 255, 0.7);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.7);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(0, 123, 255, 0);
  }
}



button.my-submit {
  margin: 20px 0 0 0px;
  border: 0;
  font-size: 14px;
  padding: 6px 25px;
  border-radius: 23px;
  outline: none;
  background-color: #115ea3;
  font-family: 'Poppins', sans-serif;
  color: #fff;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: background-color 0.6s ease-in-out, transform 0.6s ease;
}

/* Hover background color + scale effect */
button.my-submit:hover {
  background-color: #0d4e8c;
  transform: scale(1.02);
  /* slight pop effect */
}

/* Shimmer effect */
button.my-submit::before {
  content: "";
  position: absolute;
  top: 0;
  left: -75%;
  width: 50%;
  height: 100%;
  background: linear-gradient(120deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.25) 50%,
      rgba(255, 255, 255, 0.05) 100%);
  transform: skewX(-20deg);
  transition: all 0.4s ease;
}



@keyframes shimmer {
  0% {
    left: -75%;
  }

  100% {
    left: 125%;
  }
}

/* Ripple effect on focus */
button.my-submit:focus:not(:active)::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  animation: ripple 0.7s ease-out;
  width: 120%;
  height: 120%;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: translate(-50%, -50%) scale(2.5);
    opacity: 0;
  }
}

.my-sub {
  padding: 30px 30px 30px 30px;
}

button.my-upgrade {
  margin: 0px 0 0 0px;
  border: 0;
  font-size: 12px;
  padding: 4px 35px;
  border-radius: 23px;
  outline: none;
  background-color: #115ea3;
  font-family: 'Poppins', sans-serif;
  color: #fff;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: background-color 0.3s ease;
}

p.my-second-subscription {
  font-size: 16px;
  color: #1A1A1A;
  font-family: 'Poppins', sans-serif;

}

.sub-table-border {
  border: 1px solid #DFDFDF;
  border-radius: 12px;
  padding: 0 0 15px 0;
}

p.my-current.plan {
  padding: 10px 0 10px 20px;
  font-size: 14px;
  color: #1A1A1A;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  margin: 0;
}

.current-plan {
  background-color: #F5F5F5;
  border-radius: 12px 12px 0 0;
  border-bottom: 1px solid #DFDFDF;
}

p.my-freemium {
  font-size: 12px;
  padding: 16px 0 0 20px;
  font-family: 'Poppins', sans-serif;
  margin: 0;
}

p.check-my-views {
  font-size: 12px;
  margin: 15px 0 0px 0;
  font-family: 'Poppins', sans-serif;

}

p.my-billing {
  font-size: 14px;
  margin: 24px 0 14px 0;
  color: #1A1A1A;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
}

p.my-moqdom {
  font-size: 14px;
  color: #1A1A1A;
  margin: 0;
  font-family: 'Poppins', sans-serif;
}

.my-free-box {
  background-color: #fff;
  padding: 12px 13px 12px 13px;
  margin: 15px 10px 0 10px;
  border-radius: 8px;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  position: relative;
  bottom: 0;
}

p.my-ur-plan {
  margin: 15px 0 0px 10px;
  color: #8B8B8B;
  font-size: 12px;
  font-family: 'Poppins', sans-serif;
}

p.my-free-plan {
  margin: 0 0 0 10px;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;

}

p.my-sept {
  margin: 0 0 0 10px;
  font-size: 12px;
  font-weight: 500;
  font-family: 'Poppins', sans-serif;
}

p.my-account-credits-plan {
  font-size: 14px;
  font-weight: 400;
  font-family: 'Poppins', sans-serif;

}

h6.may-my-account {
  margin: 0;
  padding: 10px 0 10px 10px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
}

button.want-more-credits {
  display: block;
  margin: 20px auto;
  border: 0;
  font-size: 12px;
  padding: 4px 35px;
  border-radius: 23px;
  outline: none;
  background-color: #115ea3;
  font-family: 'Poppins', sans-serif;
  color: #fff;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: background-color 0.3s ease;
}

@keyframes earthquake-shake {
  0% {
    transform: translate(0);
  }

  10% {
    transform: translate(-2px, -2px);
  }

  20% {
    transform: translate(2px, 2px);
  }

  30% {
    transform: translate(-2px, 2px);
  }

  40% {
    transform: translate(2px, -2px);
  }

  50% {
    transform: translate(-1px, 1px);
  }

  60% {
    transform: translate(1px, -1px);
  }

  70% {
    transform: translate(-1px, 1px);
  }

  80% {
    transform: translate(1px, 1px);
  }

  90% {
    transform: translate(-1px, -1px);
  }

  100% {
    transform: translate(0);
  }
}

.shake {
  animation: earthquake-shake 0.5s ease;
}

.invoice-container {
  font-family: 'Lato', sans-serif;
  padding: 10px;
  width: 100%;
}

.invoice-vertical-scroll {
  max-height: 350px;
  /* Adjust as needed */
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.invoice-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background-color: white;
}

.invoice-table thead {
  background-color: #EDF0F4;
  position: sticky;
  top: 0;
  z-index: 1;
}

.invoice-table th,
.invoice-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #eaeaea;
  white-space: nowrap;
}

.invoice-table tr:last-child td {
  border-bottom: none;
}

.status.paid {
  color: #000;
  font-weight: 500;
}


.invoice-container {
  font-family: 'Lato', sans-serif;
  padding: 10px;
  width: 100%;
}

.invoice-vertical-scroll {
  max-height: 350px;
  overflow-y: auto;
  border-radius: 8px;
}

.invoice-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background-color: white;
}

.invoice-table th {
  padding: 10px 10px;
  text-align: left;
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  white-space: nowrap;
  font-weight: 500;
  color: #474747;

}

.invoice-table td {
  padding: 10px 10px;
  border: none;
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  white-space: nowrap;
  color: #474747;
}

.status.paid {
  color: green;
  font-weight: 500;
}


.invoice-vertical-scroll::-webkit-scrollbar {
  width: 8px;
  /* vertical */
  height: 8px;
}

.invoice-vertical-scroll::-webkit-scrollbar-track {
  background: transparent;
  box-shadow: rgba(0, 0, 0, 0.06) 0px 2px 4px 0px inset;
}

.invoice-vertical-scroll::-webkit-scrollbar-thumb {
  background-color: #DFDFDF;
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
}

/* Optional: horizontal scrollbar if ever needed */
.invoice-vertical-scroll::-webkit-scrollbar-horizontal {
  height: 8px;
  width: 8px;
}


/* Overlay transitions */
.credit-modal-overlay {
  position: fixed;
  top: 0px;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1004;
  background: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s ease;
}

.fade-in {
  opacity: 1;
}

.fade-out {
  opacity: 0;
}

/* Modal content transitions */
.credit-modal-content {
  background: white;
  padding: 30px 30px 40px 30px;
  border-radius: 10px;
  width: 485px;
  max-width: 90%;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transform: translateY(0);
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.slide-in {
  opacity: 1;
  transform: translateY(0);
}

.slide-out {
  opacity: 0;
  transform: translateY(-30px);
}

/* Close button */
.credit-modal-close {
  position: absolute;
  top: 2px;
  right: 9px;
  background: transparent;
  border: none;
  font-size: 20px;
  cursor: pointer;
}

/* Button */
.want-more-credits {
  padding: 10px 20px;
  background-color: #0066ff;
  color: white;
  font-weight: 600;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.want-more-credits:hover {
  background-color: #0052cc;
}

input.credit-input {
  outline: none;
  border: 1px solid #115EA3;
  padding: 5px 0 5px 8px;
  border-radius: 23px;
  margin: 0 11px 0 0px;
  font-size: 14px;
}

input.credit-input::placeholder{
  color: #D0D0D1;
  font-size: 12px;
}

button.credit-button {
  background-color: #115EA3;
  font-size: 14px;
  border: 1px solid #115EA3;
  padding: 5px 20px 5px 20px;
  color: #fff;
  border-radius: 18px;
}
p.kindly-check {
  font-size: 16px;
  color: #093D54;
  margin: 20px 0 20px 0;
}