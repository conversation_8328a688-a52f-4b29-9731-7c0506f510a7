import React, { useEffect, useRef } from "react";
import UseTabStore from "./useGlobalState";
import { useNavigate } from "react-router-dom";

const Alert = (props) => {
    // Get the current domain name
    const currentDomain = window.location.origin;

    const navigate = useNavigate();
    const alertRef = useRef();
    const closeAlertRef = useRef();
    const { setButtonType, buttonType, defaultErrorMsg, defaultAlert, setDefaultAlert, setDefaultErrorMsg, setViewModal, viewModal } = UseTabStore();

    useEffect(() => {
        // Add event listeners when the component mounts
        document.addEventListener('mousedown', handleOutsideClick);
        document.addEventListener('keydown', handleEscKeyPress);

        // Remove event listeners when the component unmounts
        return () => {
            document.removeEventListener('mousedown', handleOutsideClick);
            document.removeEventListener('keydown', handleEscKeyPress);
        };
    }, []);

    const handleOutsideClick = (e) => {
        // Check if the click is outside the modal
        if (!e.target.closest('.modal')) {
            close();
        }
    };

    const handleEscKeyPress = (e) => {
        // Check if the pressed key is ESC
        if (e.key === 'Escape') {
            close();
        }
    };


    const close = () => {
        setButtonType("");
        setDefaultAlert(false);
        setDefaultErrorMsg("");
        setViewModal(false);
        if (defaultAlert && defaultErrorMsg && defaultErrorMsg == "error")
            closeAlertRef?.current?.click();
    }

    useEffect(() => {
        if (defaultAlert && defaultErrorMsg && defaultErrorMsg == "error") {
            alertRef?.current?.click();
        }
    }, [])

    return (
        <>
            <button
                type="button"
                className="btn btn-info btn-md"
                data-toggle="modal"
                data-target="#alertModal"
                ref={alertRef}
                style={{ display: 'none' }} // Hide the button, it's just for triggering click programmatically
                data-backdrop="true"
            ></button>
            {defaultAlert && defaultErrorMsg && buttonType == "error" ? (
                <div className="modal fade bd-example-modal-sm show" tabIndex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" id="alertModal">
                    <div className="modal-dialog modal-sm" >
                        <div className="modal-content" style={{ borderRadius: "16px", display: "block", marginTop: "250px", marginLeft: "auto", marginRight: "auto", width: "30%" }} >
                            <div className="d-flex flex-row justify-content-end pt-2 pr-3 pb-2">
                                <button
                                    ref={closeAlertRef}
                                    type="button"
                                    className="close"
                                    data-dismiss="modal"
                                    aria-label="Close"
                                    onClick={close}
                                    style={{ display: 'none' }} // Hide the button, it's just for triggering click programmatically
                                />
                                <div onClick={close} >
                                    <img src={`${currentDomain}/images/cross.png`} style={{ cursor: "pointer" }} />
                                </div>
                            </div>
                            <div className="d-flex flex-row justify-content-center">
                                <div>
                                    <img src={`${currentDomain}/images/grey-avetar.png`} width="50" />
                                </div>
                                <div>
                                    <p className="grey-avetar">!</p>
                                </div>
                                <div>
                                    <p className="anerror-red">An Error Occurred</p>
                                </div>
                            </div>

                            <div className="lorem">
                                <p>{props.data}</p>
                            </div>

                            <div className="upgrdbutton">
                                <button type="button" onClick={close}>Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <></>
            )}

        </>
    )
}

export default Alert;